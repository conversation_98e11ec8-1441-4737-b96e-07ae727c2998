# =============================================================================
# 模式一：Lightning模式主配置文件
# =============================================================================
# 功能：纯PyTorch Lightning训练，禁用WandB实验跟踪
# 特点：
# - 完整的Loguru日志系统（INFO级别控制台输出）
# - 禁用WandB Logger组件
# - 显示系统资源监控
# - 标准训练流程和回调
#
# 使用方式：
#   python scripts/train.py --config-name=config_mode1_lightning
# =============================================================================

defaults:
  - _self_                    # 当前文件的配置优先级最高
  - trainer: default          # 训练器配置：GPU设置、训练轮数、精度等
  - model: deeplabv3          # 模型配置：默认使用DeepLabV3+语义分割模型
  - data: suide               # 数据集配置：默认使用SuiDe遥感数据集
  - loss: combined            # 损失函数配置：默认使用组合损失函数(CE+Dice+Focal)
  - optimizer: adamw          # 优化器配置：默认使用AdamW优化器
  - scheduler: cosine         # 学习率调度器：默认使用余弦退火调度器
  - logger: wandb_disabled    # 关键差异：禁用WandB Logger
  - callbacks: default        # 回调函数：默认使用标准回调(检查点、早停等)
  - ui: loguru_config         # UI配置：使用Loguru统一日志系统
  - experiment: null          # 实验配置：默认不使用预定义实验配置

# =============================================================================
# 模式一特定配置
# =============================================================================
# 全局项目设定
project_name: "SuiDe_RemoteSensing"
experiment_name: "lightning_mode"                                     # 模式一专用实验名称
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"

# 训练模式标识（用于Loguru模式检测）
training_mode: "lightning"

# WandB配置（虽然禁用，但保留结构以防需要）
wandb:
  project: ${project_name}
  name: ${run_name}
  tags: ["lightning-only", "no-wandb", "${model.name}"]
  notes: "Pure Lightning training without WandB experiment tracking."
  mode: disabled                                                      # 明确禁用
  local_dir: null
  log_model: false
  silent: true
