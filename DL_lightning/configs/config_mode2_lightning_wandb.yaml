# =============================================================================
# 模式二：Lightning+WandB模式主配置文件
# =============================================================================
# 功能：PyTorch Lightning训练 + WandB实验跟踪
# 特点：
# - 完整的Loguru日志系统（INFO级别控制台输出）
# - 启用WandB Logger组件（自动模式检测）
# - 显示系统资源监控
# - 实验跟踪和指标可视化
#
# 使用方式：
#   python scripts/train.py --config-name=config_mode2_lightning_wandb
#   python scripts/train.py --config-name=config_mode2_lightning_wandb logger=wandb_offline  # 强制离线
#   python scripts/train.py --config-name=config_mode2_lightning_wandb logger=wandb_online   # 强制在线
# =============================================================================

defaults:
  - _self_                    # 当前文件的配置优先级最高
  - trainer: default          # 训练器配置：GPU设置、训练轮数、精度等
  - model: deeplabv3          # 模型配置：默认使用DeepLabV3+语义分割模型
  - data: suide               # 数据集配置：默认使用SuiDe遥感数据集
  - loss: combined            # 损失函数配置：默认使用组合损失函数(CE+Dice+Focal)
  - optimizer: adamw          # 优化器配置：默认使用AdamW优化器
  - scheduler: cosine         # 学习率调度器：默认使用余弦退火调度器
  - logger: wandb             # 关键差异：启用WandB Logger（自动模式）
  - callbacks: default        # 回调函数：默认使用标准回调(检查点、早停等)
  - ui: loguru_config         # UI配置：使用Loguru统一日志系统
  - experiment: null          # 实验配置：默认不使用预定义实验配置

# =============================================================================
# 模式二特定配置
# =============================================================================
# 全局项目设定
project_name: "SuiDe_RemoteSensing"
experiment_name: "lightning_wandb_mode"                               # 模式二专用实验名称
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"

# 训练模式标识（用于Loguru模式检测）
training_mode: "lightning_wandb"

# WandB配置（完整配置）
wandb:
  project: ${project_name}
  name: ${run_name}
  tags: ["lightning-wandb", "experiment-tracking", "${model.name}"]
  notes: "Lightning training with WandB experiment tracking and visualization."
  
  # 自动模式检测配置
  mode: auto                                                          # 自动检测最佳模式
  timeout: 10
  retry_attempts: 3
  
  # 存储配置
  local_dir: null                                                     # 使用Hydra输出目录
  log_model: false                                                    # 根据需要启用
  silent: true
  
  # API配置
  api_key: null                                                       # 建议通过环境变量设置
