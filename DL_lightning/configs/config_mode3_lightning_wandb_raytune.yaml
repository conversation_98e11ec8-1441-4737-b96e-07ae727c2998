# =============================================================================
# 模式三：Lightning+WandB+RayTune模式主配置文件
# =============================================================================
# 功能：超参数优化训练，支持并行试验和自动调优
# 特点：
# - Loguru日志系统（WARNING级别，减少输出噪音）
# - 启用WandB Logger用于跟踪多个试验
# - 禁用系统资源监控（减少多进程开销）
# - Ray Tune超参数搜索和调度
#
# 使用方式：
#   python scripts/tune.py --config-name=config_mode3_lightning_wandb_raytune
# =============================================================================

defaults:
  - _self_                    # 当前文件的配置优先级最高
  - trainer: default          # 训练器配置：GPU设置、训练轮数、精度等
  - model: unet               # 模型配置：HPO通常使用较轻量的模型
  - data: suide               # 数据集配置：默认使用SuiDe遥感数据集
  - loss: ce_dice             # 损失函数配置：HPO使用简化的损失函数
  - optimizer: adamw          # 优化器配置：默认使用AdamW优化器
  - scheduler: cosine         # 学习率调度器：默认使用余弦退火调度器
  - logger: wandb             # 启用WandB Logger用于试验跟踪
  - callbacks: default        # 回调函数：默认使用标准回调
  - ui: loguru_config         # UI配置：使用Loguru统一日志系统
  - experiment: null          # 实验配置：默认不使用预定义实验配置

# =============================================================================
# 模式三特定配置
# =============================================================================
# 全局项目设定
project_name: "SuiDe_RemoteSensing"
experiment_name: "lightning_wandb_raytune_mode"                       # 模式三专用实验名称
run_name: "${experiment_name}_${now:%Y-%m-%d_%H-%M-%S}"

# 训练模式标识（用于Loguru模式检测）
training_mode: "lightning_wandb_raytune"

# WandB配置（针对HPO优化）
wandb:
  project: ${project_name}
  name: ${run_name}
  tags: ["lightning-wandb-raytune", "hpo", "hyperparameter-optimization", "${model.name}"]
  notes: "Hyperparameter optimization with Lightning, WandB tracking, and Ray Tune."
  
  # HPO环境下的配置
  mode: auto
  timeout: 5                                                          # 更短的超时时间
  retry_attempts: 2                                                   # 更少的重试次数
  
  # 存储配置
  local_dir: null
  log_model: false                                                    # HPO期间不保存模型
  silent: true

# =============================================================================
# Ray Tune HPO 配置
# =============================================================================
hpo:
  experiment_name: "unet_hpo_basic"
  
  # Ray Tune 运行参数
  run_params:
    name: "unet_hpo_mode3"
    num_samples: 5                                                    # 试验数量
    local_dir: "${get_project_root:}/experiments_output/ray_results"
  
  # Ray 集群配置
  ray_init_args:
    _target_: ray.init
    num_cpus: 4
    num_gpus: 1
  
  # 资源配置
  resources_per_trial:
    use_gpu: true
    CPU: 1
    GPU: 0.5                                                          # 允许GPU共享
  
  # 调度器配置
  scheduler:
    _target_: ray.tune.schedulers.ASHAScheduler
    metric: "val_iou"
    mode: "max"
    max_t: 10                                                         # 最大训练轮数
    grace_period: 2
    reduction_factor: 2
  
  # 搜索空间
  search_space:
    lr:
      min: 1e-5
      max: 1e-2
    batch_size:
      - 8
      - 16
      - 32
