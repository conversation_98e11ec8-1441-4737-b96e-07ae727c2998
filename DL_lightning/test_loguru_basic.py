#!/usr/bin/env python
import sys
sys.path.insert(0, 'src')

def test_basic_import():
    try:
        from utils.loguru_console_manager import LoguruConsoleManager
        from callbacks.loguru_progress_callback import LoguruProgressCallback
        print("✅ 基础导入测试通过")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_functionality():
    try:
        from utils.loguru_console_manager import LoguruConsoleManager
        manager = LoguruConsoleManager()
        manager.print_info("测试信息")
        manager.print_success("测试成功")
        print("✅ 基础功能测试通过")
        return True
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_basic_import() and test_basic_functionality()
    sys.exit(0 if success else 1)
