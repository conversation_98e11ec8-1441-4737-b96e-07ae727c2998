#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DL_lightning三种训练模式完整测试

测试三种核心训练模式:
1. Lightning模式 - 纯PyTorch Lightning训练
2. Lightning+WandB模式 - 带实验跟踪的训练  
3. Lightning+WandB+RayTune模式 - 超参数优化训练

验证Loguru控制台输出在所有模式下的正确性
"""

import sys
import os
import time
import subprocess
import tempfile
from pathlib import Path
from typing import Dict, List, Optional

# 添加项目路径
sys.path.insert(0, 'src')

def run_command(cmd: List[str], timeout: int = 300) -> Dict[str, any]:
    """运行命令并返回结果"""
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=timeout,
            cwd=os.getcwd()
        )
        
        return {
            'success': result.returncode == 0,
            'returncode': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'command': ' '.join(cmd)
        }
    except subprocess.TimeoutExpired:
        return {
            'success': False,
            'returncode': -1,
            'stdout': '',
            'stderr': f'命令超时 ({timeout}秒)',
            'command': ' '.join(cmd)
        }
    except Exception as e:
        return {
            'success': False,
            'returncode': -1,
            'stdout': '',
            'stderr': str(e),
            'command': ' '.join(cmd)
        }

def check_loguru_output_patterns(output: str) -> Dict[str, bool]:
    """检查Loguru输出模式"""
    patterns = {
        'loguru_info': '[INFO]' in output,
        'loguru_success': '[SUCCESS]' in output,
        'loguru_warning': '[WARNING]' in output,
        'loguru_error': '[ERROR]' in output,
        'progress_tracking': '[PROGRESS]' in output,
        'startup_banner': '正在实例化' in output,
        'training_start': '开始训练' in output,
        'no_rich_errors': 'rich' not in output.lower() or 'RichHandler' not in output
    }
    
    return patterns

def test_lightning_mode():
    """测试模式1: 纯Lightning训练"""
    print("\n" + "="*60)
    print("🧪 测试模式1: Lightning模式")
    print("="*60)
    
    # 使用fast_dev_run进行快速测试
    cmd = [
        'python', 'scripts/train.py',
        'trainer.fast_dev_run=true',
        'trainer.devices=1',
        'data.dataloader_config.batch_size=2',
        'model.num_classes=14'
    ]
    
    result = run_command(cmd, timeout=120)
    
    if result['success']:
        patterns = check_loguru_output_patterns(result['stdout'])
        print("✅ Lightning模式测试成功")
        print(f"   - Loguru信息输出: {'✅' if patterns['loguru_info'] else '❌'}")
        print(f"   - 启动横幅: {'✅' if patterns['startup_banner'] else '❌'}")
        print(f"   - 训练开始: {'✅' if patterns['training_start'] else '❌'}")
        print(f"   - 无Rich错误: {'✅' if patterns['no_rich_errors'] else '❌'}")
        return True, patterns
    else:
        print("❌ Lightning模式测试失败")
        print(f"   错误: {result['stderr']}")
        return False, {}

def test_lightning_wandb_mode():
    """测试模式2: Lightning+WandB训练"""
    print("\n" + "="*60)
    print("🧪 测试模式2: Lightning+WandB模式")
    print("="*60)
    
    # 使用离线模式避免网络依赖
    cmd = [
        'python', 'scripts/train.py',
        'trainer.fast_dev_run=true',
        'trainer.devices=1',
        'logger=wandb_offline',  # 使用离线WandB
        'wandb.mode=offline',
        'data.dataloader_config.batch_size=2',
        'model.num_classes=14'
    ]
    
    result = run_command(cmd, timeout=120)
    
    if result['success']:
        patterns = check_loguru_output_patterns(result['stdout'])
        wandb_patterns = {
            'wandb_init': 'wandb' in result['stdout'].lower(),
            'offline_mode': 'offline' in result['stdout'].lower()
        }
        
        print("✅ Lightning+WandB模式测试成功")
        print(f"   - Loguru信息输出: {'✅' if patterns['loguru_info'] else '❌'}")
        print(f"   - WandB初始化: {'✅' if wandb_patterns['wandb_init'] else '❌'}")
        print(f"   - 离线模式: {'✅' if wandb_patterns['offline_mode'] else '❌'}")
        print(f"   - 无Rich错误: {'✅' if patterns['no_rich_errors'] else '❌'}")
        return True, {**patterns, **wandb_patterns}
    else:
        print("❌ Lightning+WandB模式测试失败")
        print(f"   错误: {result['stderr']}")
        return False, {}

def test_lightning_wandb_raytune_mode():
    """测试模式3: Lightning+WandB+RayTune训练"""
    print("\n" + "="*60)
    print("🧪 测试模式3: Lightning+WandB+RayTune模式")
    print("="*60)
    
    # 创建临时的简化HPO配置
    temp_config = """
# @package _global_
defaults:
  - _self_
  - model: unet
  - data: suide
  - optimizer: adamw
  - scheduler: cosine
  - loss: ce_dice
  - callbacks: default
  - logger: wandb_disabled

# HPO配置
hpo:
  ray_init_args:
    _target_: ray.init
    local_mode: true
    ignore_reinit_error: true
    
  search_space:
    lr:
      min: 0.001
      max: 0.01
    batch_size: [2, 4]
    
  scheduler:
    _target_: ray.tune.schedulers.ASHAScheduler
    metric: val/iou
    mode: max
    max_t: 2
    grace_period: 1
    
  run_params:
    num_samples: 2
    name: test_hpo
    local_dir: ./test_ray_results
    
  resources_per_trial:
    CPU: 1
    GPU: 0

# 训练器配置
trainer:
  fast_dev_run: true
  devices: 1
  max_epochs: 1
  
# 数据配置
data:
  dataloader_config:
    batch_size: 2
  
# 模型配置
model:
  num_classes: 14
"""
    
    # 保存临时配置
    temp_config_path = Path("configs/hpo/test_tune.yaml")
    temp_config_path.write_text(temp_config)
    
    try:
        cmd = [
            'python', 'scripts/tune.py',
            '--config-name=tune_basic',
            'hpo.run_params.num_samples=1',
            'hpo.scheduler.max_t=1',
            'trainer.fast_dev_run=true',
            'data.dataloader_config.batch_size=2'
        ]
        
        result = run_command(cmd, timeout=180)
        
        if result['success']:
            patterns = check_loguru_output_patterns(result['stdout'])
            raytune_patterns = {
                'ray_init': 'ray' in result['stdout'].lower(),
                'tune_start': 'tune' in result['stdout'].lower() or 'hpo' in result['stdout'].lower()
            }
            
            print("✅ Lightning+WandB+RayTune模式测试成功")
            print(f"   - Loguru信息输出: {'✅' if patterns['loguru_info'] else '❌'}")
            print(f"   - Ray初始化: {'✅' if raytune_patterns['ray_init'] else '❌'}")
            print(f"   - Tune启动: {'✅' if raytune_patterns['tune_start'] else '❌'}")
            print(f"   - 无Rich错误: {'✅' if patterns['no_rich_errors'] else '❌'}")
            return True, {**patterns, **raytune_patterns}
        else:
            print("❌ Lightning+WandB+RayTune模式测试失败")
            print(f"   错误: {result['stderr']}")
            return False, {}
            
    finally:
        # 清理临时文件
        if temp_config_path.exists():
            temp_config_path.unlink()

def test_training_mode_detection():
    """测试训练模式自动检测"""
    print("\n" + "="*60)
    print("🧪 测试训练模式自动检测")
    print("="*60)
    
    try:
        from utils.loguru_console_manager import LoguruConsoleManager, TrainingMode
        
        # 测试不同环境下的模式检测
        manager = LoguruConsoleManager()
        
        # 模拟不同环境
        test_cases = [
            ("无特殊环境", {}),
            ("WandB环境", {"WANDB_PROJECT": "test"}),
            ("Ray环境", {"RAY_ADDRESS": "local"}),
        ]
        
        for case_name, env_vars in test_cases:
            # 临时设置环境变量
            old_env = {}
            for key, value in env_vars.items():
                old_env[key] = os.environ.get(key)
                os.environ[key] = value
            
            try:
                mode = manager.detect_training_mode()
                print(f"   {case_name}: {mode.value}")
            finally:
                # 恢复环境变量
                for key, old_value in old_env.items():
                    if old_value is None:
                        os.environ.pop(key, None)
                    else:
                        os.environ[key] = old_value
        
        print("✅ 训练模式检测测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 训练模式检测测试失败: {e}")
        return False

def run_all_tests():
    """运行所有三种模式测试"""
    print("🚀 开始DL_lightning三种训练模式完整测试")
    print("=" * 80)
    
    results = {}
    
    # 测试训练模式检测
    results['mode_detection'] = test_training_mode_detection()
    
    # 测试三种训练模式
    results['lightning'], _ = test_lightning_mode()
    results['lightning_wandb'], _ = test_lightning_wandb_mode()
    results['lightning_wandb_raytune'], _ = test_lightning_wandb_raytune_mode()
    
    # 生成测试报告
    print("\n" + "=" * 80)
    print("📊 测试结果总结")
    print("=" * 80)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有三种训练模式测试通过！Loguru迁移完全成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
