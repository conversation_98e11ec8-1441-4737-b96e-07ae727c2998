#!/bin/bash
# =============================================================================
# 模式一：Lightning模式运行脚本
# =============================================================================
# 功能：纯PyTorch Lightning训练，禁用WandB实验跟踪
# 特点：
# - 使用Loguru日志系统进行控制台输出和文件记录
# - 禁用WandB Logger组件，不进行实验跟踪
# - 完整的训练流程，包括验证和检查点保存
# - 系统监控：CPU、内存、GPU使用率实时显示
#
# 使用方式：
#   chmod +x bash/mode1_lightning.sh
#   ./bash/mode1_lightning.sh
# =============================================================================

echo "🚀 启动模式一：Lightning模式训练"
echo "============================================================"
echo "训练特点："
echo "- 纯PyTorch Lightning训练"
echo "- 禁用WandB实验跟踪"
echo "- 完整Loguru日志系统"
echo "- 系统资源监控"
echo "============================================================"

# 进入项目根目录
cd "$(dirname "$0")/.."

# 执行训练命令
python scripts/train.py \
    logger=wandb_disabled \
    trainer.max_epochs=50 \
    trainer.devices=auto \
    trainer.precision="16-mixed" \
    model=deeplabv3 \
    data=suide \
    optimizer=adamw \
    scheduler=cosine \
    loss=combined

echo "============================================================"
echo "✅ 模式一训练完成"
echo "============================================================"
