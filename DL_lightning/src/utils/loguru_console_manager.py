#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
基于Loguru的统一控制台输出管理器

功能特性:
- 统一输出拦截 - 通过InterceptHandler拦截所有输出源
- 训练模式检测 - 自动识别Lightning/WandB/RayTune组合
- 双重输出格式 - 控制台简洁 + 文件详细
- 系统监控集成 - CPU/GPU/内存实时监控
- 向后兼容 - 保持现有API接口不变
"""

import os
import sys
import logging
import inspect
import tempfile
from enum import Enum
from typing import Optional, Dict, Any, Union
from contextlib import contextmanager
from pathlib import Path

from loguru import logger
from omegaconf import DictConfig

try:
    from .system_monitor import SystemMonitor
    SYSTEM_MONITOR_AVAILABLE = True
except ImportError:
    SYSTEM_MONITOR_AVAILABLE = False


class TrainingMode(Enum):
    """训练模式枚举"""
    LIGHTNING = "lightning"
    LIGHTNING_WANDB = "lightning_wandb"
    LIGHTNING_WANDB_RAYTUNE = "lightning_wandb_raytune"


class InterceptHandler(logging.Handler):
    """Loguru拦截处理器 - 统一所有日志输出"""
    
    def emit(self, record):
        """拦截标准logging输出并重定向到Loguru"""
        # 获取对应的Loguru级别
        try:
            level = logger.level(record.levelname).name
        except ValueError:
            level = record.levelno
        
        # 查找调用者信息
        frame, depth = logging.currentframe(), 2
        while frame and frame.f_code.co_filename == logging.__file__:
            frame = frame.f_back
            depth += 1
        
        # 发送到Loguru
        logger.opt(depth=depth, exception=record.exc_info).log(
            level, record.getMessage()
        )


class LoguruConsoleManager:
    """基于Loguru的统一控制台输出管理器"""
    
    def __init__(self, config: Optional[DictConfig] = None):
        """初始化Loguru控制台管理器"""
        self.config = config or {}
        self.system_monitor = None
        self.log_file_path = None
        self.is_ray_tune_worker = False
        self.training_mode = None
        
        # 初始化系统监控
        if SYSTEM_MONITOR_AVAILABLE:
            try:
                self.system_monitor = SystemMonitor()
            except Exception:
                self.system_monitor = None
        
        # 设置Loguru
        self.setup_loguru()
        self.setup_interception()
        
        # 检测训练模式
        self.training_mode = self.detect_training_mode()
        self.is_ray_tune_worker = self.training_mode == TrainingMode.LIGHTNING_WANDB_RAYTUNE
    
    def setup_loguru(self):
        """配置Loguru日志系统"""
        # 移除默认处理器
        logger.remove()
        
        # 获取配置
        console_config = self.config.get('console', {})
        file_config = self.config.get('file', {})
        
        # 控制台输出 - 简洁格式
        console_format = console_config.get('format', '[{level}] {message}')
        console_level = console_config.get('level', 'INFO')
        colorize = console_config.get('colorize', True)
        
        logger.add(
            sys.stdout,
            format=console_format,
            level=console_level,
            colorize=colorize,
            filter=self._console_filter
        )
        
        # 文件输出 - 详细格式
        if file_config:
            self.log_file_path = self._get_log_file_path()
            file_format = file_config.get(
                'format', 
                '{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {name}:{function}:{line} | {message}'
            )
            file_level = file_config.get('level', 'DEBUG')
            rotation = file_config.get('rotation', '100 MB')
            retention = file_config.get('retention', '30 days')
            compression = file_config.get('compression', 'zip')
            enqueue = file_config.get('enqueue', True)
            
            logger.add(
                self.log_file_path,
                format=file_format,
                level=file_level,
                rotation=rotation,
                retention=retention,
                compression=compression,
                enqueue=enqueue
            )
    
    def _console_filter(self, record):
        """控制台输出过滤器"""
        # 在Ray Tune环境下减少输出
        if self.is_ray_tune_worker:
            return record["level"].no >= logger.level("WARNING").no
        return True
    
    def _get_log_file_path(self) -> str:
        """获取日志文件路径"""
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)
        return str(logs_dir / "training.log")
    
    def setup_interception(self):
        """设置全局输出拦截"""
        # 拦截标准logging
        logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)
        
        # 拦截特定库的日志
        for logger_name in ["pytorch_lightning", "lightning", "wandb"]:
            logging.getLogger(logger_name).handlers = [InterceptHandler()]
    
    def detect_training_mode(self) -> TrainingMode:
        """自动检测当前训练模式"""
        # 检测Ray Tune环境
        ray_tune_detected = (
            os.environ.get("TUNE_TRIAL_ID") is not None or
            "ray.tune" in sys.modules or
            any("tune" in str(frame.filename) for frame in inspect.stack())
        )
        
        # 检测WandB环境  
        wandb_detected = (
            "wandb" in sys.modules or
            os.environ.get("WANDB_PROJECT") is not None or
            hasattr(self, '_wandb_logger_detected')
        )
        
        # 返回对应模式
        if ray_tune_detected and wandb_detected:
            return TrainingMode.LIGHTNING_WANDB_RAYTUNE
        elif wandb_detected:
            return TrainingMode.LIGHTNING_WANDB
        else:
            return TrainingMode.LIGHTNING
    
    # === 向后兼容方法 ===
    
    def print_info(self, message: str, emoji: str = ""):
        """信息输出 - 兼容原有API"""
        logger.info(message)
    
    def print_success(self, message: str, emoji: str = ""):
        """成功输出 - 兼容原有API"""
        logger.success(message)
    
    def print_warning(self, message: str, emoji: str = ""):
        """警告输出 - 兼容原有API"""
        logger.warning(message)
    
    def print_error(self, message: str, emoji: str = ""):
        """错误输出 - 兼容原有API"""
        logger.error(message)
    
    def print_startup_banner(self, cfg: DictConfig):
        """启动横幅 - 兼容原有API"""
        logger.info("=" * 60)
        logger.info("DL_lightning 训练启动")
        logger.info("=" * 60)
        logger.info(f"项目名称: {cfg.get('project_name', 'DL_lightning')}")
        logger.info(f"实验名称: {cfg.get('experiment_name', 'default')}")
        logger.info(f"训练模式: {self.training_mode.value}")
        logger.info("=" * 60)
    
    def print_wandb_status(self, status: Dict):
        """WandB状态 - 兼容原有API"""
        logger.info(f"WandB状态: {status.get('mode', 'unknown')}")
        if 'project' in status:
            logger.info(f"项目: {status['project']}")
        if 'run_id' in status:
            logger.info(f"运行ID: {status['run_id']}")
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        if self.system_monitor:
            stats = self.system_monitor.get_current_stats()
            return {
                'cpu_percent': stats.cpu_percent,
                'memory_percent': stats.memory_percent,
                'memory_used_gb': stats.memory_used_gb,
                'memory_total_gb': stats.memory_total_gb,
                'gpu_stats': stats.gpu_stats,
                'timestamp': stats.timestamp
            }
        return {}
    
    # === 上下文管理器 ===
    
    @contextmanager
    def status(self, message: str):
        """状态显示上下文管理器"""
        logger.info(f"开始: {message}")
        try:
            yield
        finally:
            logger.info(f"完成: {message}")
    
    @contextmanager 
    def progress_context(self, description: str, total: int):
        """进度跟踪上下文管理器"""
        class ProgressTracker:
            def __init__(self, desc, total_items):
                self.description = desc
                self.total = total_items
                self.current = 0
                
            def update(self, n=1):
                self.current += n
                percentage = (self.current / self.total) * 100
                logger.info(f"[PROGRESS] {self.description}: {percentage:.1f}% ({self.current}/{self.total})")
        
        tracker = ProgressTracker(description, total)
        logger.info(f"开始进度跟踪: {description}")
        try:
            yield tracker
        finally:
            logger.info(f"进度跟踪完成: {description}")
    
    @contextmanager
    def intercept_output(self):
        """输出拦截上下文管理器"""
        # 在这个上下文中，所有输出都会被拦截
        yield

    def start_output_intercept(self):
        """启动全局输出拦截"""
        # Loguru的InterceptHandler已经在初始化时设置，这里只是兼容性方法
        pass

    def stop_output_intercept(self):
        """停止全局输出拦截"""
        # Loguru的InterceptHandler会自动处理，这里只是兼容性方法
        pass

    def update_trial_status(self, trial_id: str, status: str, config: dict, metrics: dict, wandb_info: dict = None):
        """更新Ray Tune试验状态"""
        status_emoji = {
            "RUNNING": "🏃",
            "TERMINATED": "✅",
            "ERROR": "❌",
            "PENDING": "⏳"
        }.get(status, "📊")

        # 格式化配置信息
        config_str = ", ".join([f"{k}={v}" for k, v in config.items() if not k.startswith('_')])

        # 格式化指标信息
        metrics_str = ""
        if metrics:
            key_metrics = {k: v for k, v in metrics.items() if k in ['val_iou', 'val_loss', 'train_loss']}
            if key_metrics:
                metrics_str = " | " + ", ".join([f"{k}={v:.4f}" for k, v in key_metrics.items()])

        # 输出试验状态
        message = f"Trial {trial_id[:8]} [{status}] {config_str}{metrics_str}"

        if status == "ERROR":
            logger.error(f"{status_emoji} {message}")
        elif status == "TERMINATED":
            logger.success(f"{status_emoji} {message}")
        else:
            logger.info(f"{status_emoji} {message}")

    def stop_dashboard(self):
        """停止仪表盘 (兼容性方法)"""
        # Loguru不需要特殊的仪表盘停止操作
        logger.info("🛑 HPO仪表盘停止")


# === 全局实例管理 ===

_global_console_manager: Optional[LoguruConsoleManager] = None


def setup_console_manager(cfg: DictConfig, verbose: bool = True) -> LoguruConsoleManager:
    """设置控制台管理器"""
    global _global_console_manager
    
    # 获取UI配置
    ui_config = cfg.get('ui', {})
    
    _global_console_manager = LoguruConsoleManager(ui_config)
    
    if verbose:
        _global_console_manager.print_startup_banner(cfg)
    
    return _global_console_manager


def get_console_manager() -> LoguruConsoleManager:
    """获取全局控制台管理器实例"""
    global _global_console_manager
    
    if _global_console_manager is None:
        # 创建默认实例
        _global_console_manager = LoguruConsoleManager()
    
    return _global_console_manager
