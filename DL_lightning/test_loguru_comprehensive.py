#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Loguru迁移综合测试脚本

测试内容:
1. 基础功能测试
2. 配置加载测试  
3. 输出拦截测试
4. 训练模式检测测试
5. 系统监控集成测试
6. 向后兼容性测试
"""

import sys
import os
import tempfile
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, 'src')

def test_basic_functionality():
    """测试基础功能"""
    print("🧪 测试1: 基础功能测试")
    try:
        from utils.loguru_console_manager import LoguruConsoleManager, get_console_manager
        from callbacks.loguru_progress_callback import LoguruProgressCallback
        
        # 创建管理器实例
        manager = LoguruConsoleManager()
        
        # 测试基础输出方法
        manager.print_info("测试信息输出")
        manager.print_success("测试成功输出")
        manager.print_warning("测试警告输出")
        manager.print_error("测试错误输出")
        
        # 测试全局实例
        global_manager = get_console_manager()
        assert global_manager is not None
        
        print("✅ 基础功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基础功能测试失败: {e}")
        return False

def test_configuration_loading():
    """测试配置加载"""
    print("\n🧪 测试2: 配置加载测试")
    try:
        from omegaconf import OmegaConf
        from utils.loguru_console_manager import LoguruConsoleManager
        
        # 加载配置文件
        config_path = Path("configs/ui/loguru_config.yaml")
        if config_path.exists():
            config = OmegaConf.load(config_path)
            manager = LoguruConsoleManager(config)
            print("✅ 配置加载测试通过")
            return True
        else:
            print("⚠️ 配置文件不存在，跳过配置加载测试")
            return True
            
    except Exception as e:
        print(f"❌ 配置加载测试失败: {e}")
        return False

def test_output_interception():
    """测试输出拦截功能"""
    print("\n🧪 测试3: 输出拦截测试")
    try:
        from utils.loguru_console_manager import LoguruConsoleManager
        
        manager = LoguruConsoleManager()
        
        # 测试标准logging拦截
        logger = logging.getLogger("test_logger")
        logger.info("这是一条测试日志")
        
        # 测试print拦截
        with manager.intercept_output():
            print("这是一条被拦截的print输出")
        
        print("✅ 输出拦截测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 输出拦截测试失败: {e}")
        return False

def test_training_mode_detection():
    """测试训练模式检测"""
    print("\n🧪 测试4: 训练模式检测测试")
    try:
        from utils.loguru_console_manager import LoguruConsoleManager, TrainingMode
        
        manager = LoguruConsoleManager()
        mode = manager.detect_training_mode()
        
        assert mode in [TrainingMode.LIGHTNING, TrainingMode.LIGHTNING_WANDB, TrainingMode.LIGHTNING_WANDB_RAYTUNE]
        print(f"✅ 训练模式检测测试通过，当前模式: {mode.value}")
        return True
        
    except Exception as e:
        print(f"❌ 训练模式检测测试失败: {e}")
        return False

def test_system_monitoring():
    """测试系统监控集成"""
    print("\n🧪 测试5: 系统监控集成测试")
    try:
        from utils.loguru_console_manager import LoguruConsoleManager
        
        manager = LoguruConsoleManager()
        stats = manager.get_system_stats()
        
        if stats:
            print(f"✅ 系统监控集成测试通过，获取到统计信息: {list(stats.keys())}")
        else:
            print("⚠️ 系统监控不可用，但测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统监控集成测试失败: {e}")
        return False

def test_context_managers():
    """测试上下文管理器"""
    print("\n🧪 测试6: 上下文管理器测试")
    try:
        from utils.loguru_console_manager import LoguruConsoleManager
        
        manager = LoguruConsoleManager()
        
        # 测试状态上下文管理器
        with manager.status("测试操作"):
            pass
        
        # 测试进度上下文管理器
        with manager.progress_context("测试进度", 10) as progress:
            for i in range(5):
                progress.update(1)
        
        print("✅ 上下文管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 上下文管理器测试失败: {e}")
        return False

def test_callback_functionality():
    """测试回调功能"""
    print("\n🧪 测试7: 回调功能测试")
    try:
        from callbacks.loguru_progress_callback import LoguruProgressCallback, LoguruModelSummaryCallback
        
        # 创建回调实例
        progress_callback = LoguruProgressCallback()
        summary_callback = LoguruModelSummaryCallback()
        
        assert progress_callback is not None
        assert summary_callback is not None
        
        print("✅ 回调功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 回调功能测试失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🧪 测试8: 向后兼容性测试")
    try:
        from utils.loguru_console_manager import setup_console_manager, get_console_manager
        from omegaconf import DictConfig
        
        # 创建模拟配置
        mock_config = DictConfig({
            'project_name': 'test_project',
            'experiment_name': 'test_experiment'
        })
        
        # 测试setup函数
        manager = setup_console_manager(mock_config, verbose=False)
        assert manager is not None
        
        # 测试全局获取函数
        global_manager = get_console_manager()
        assert global_manager is manager
        
        print("✅ 向后兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return False

def test_import_compatibility():
    """测试导入兼容性"""
    print("\n🧪 测试9: 导入兼容性测试")
    try:
        # 测试所有更新后的导入
        from utils.loguru_console_manager import get_console_manager
        
        # 模拟其他模块的导入
        test_imports = [
            "from utils.loguru_console_manager import get_console_manager",
            "from callbacks.loguru_progress_callback import LoguruProgressCallback"
        ]
        
        for import_stmt in test_imports:
            try:
                exec(import_stmt)
            except ImportError as e:
                print(f"❌ 导入失败: {import_stmt} - {e}")
                return False
        
        print("✅ 导入兼容性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 导入兼容性测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始Loguru迁移综合测试")
    print("=" * 60)
    
    tests = [
        test_basic_functionality,
        test_configuration_loading,
        test_output_interception,
        test_training_mode_detection,
        test_system_monitoring,
        test_context_managers,
        test_callback_functionality,
        test_backward_compatibility,
        test_import_compatibility
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ 测试异常: {test.__name__} - {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed} 通过, {failed} 失败")
    
    if failed == 0:
        print("🎉 所有测试通过！Loguru迁移成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要检查问题")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
